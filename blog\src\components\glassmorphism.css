/* Glassmorphism 2.0 Styles */
:root {
  --glass-bg: rgba(255, 255, 255, 0.02);
  --glass-border: rgba(255, 255, 255, 0.1);
  --glass-shadow: rgba(0, 0, 0, 0.3);
  --glass-backdrop: blur(20px);
  --refraction-intensity: 2px;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  background: #000;
  width: 100%;
  min-width: 100%;
}

body {
  font-family: 'Varela Round', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  overflow-x: hidden;
  background: #000;
  margin: 0;
  padding: 0;
  min-height: 100vh;
  height: auto;
  width: 100%;
  min-width: 100vw;
  display: block !important;
  place-items: unset !important;
}

/* Background Container */
.background-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  min-width: 100%;
  min-height: 100%;
  z-index: -10;
  overflow: hidden;
  background: #000;
}

/* Glass Navbar */
.navbar-wrapper {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  width: calc(100% - 40px);
  max-width: 1200px;
  z-index: 1000;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.glass-navbar {
  flex: 1;
  
  /* Reduced frost glassmorphism */
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(8px) saturate(150%);
  -webkit-backdrop-filter: blur(8px) saturate(150%);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    inset 0 -1px 0 rgba(255, 255, 255, 0.1);
  
  padding: 5px 28px;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.glass-navbar:hover {
  backdrop-filter: blur(12px) saturate(180%);
  -webkit-backdrop-filter: blur(12px) saturate(180%);
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 255, 255, 0.25);
}

/* Edge Refraction Effects for Glass Navbar - Only on rounded corners */
.glass-navbar::before,
.glass-navbar::after {
  position: absolute;
  content: '';
  pointer-events: none;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  border-radius: 20px;
}

/* Top rounded edge refraction */
.glass-navbar:hover::before {
  top: -1px;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    transparent 15%,
    rgba(255, 255, 255, 0.8) 25%,
    rgba(255, 255, 255, 0.9) 50%,
    rgba(255, 255, 255, 0.8) 75%,
    transparent 85%,
    transparent 100%
  );
  opacity: 1;
  border-radius: 20px 20px 0 0;
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
  mask: radial-gradient(ellipse 80% 100% at 50% 0%, black 60%, transparent 70%);
  -webkit-mask: radial-gradient(ellipse 80% 100% at 50% 0%, black 60%, transparent 70%);
}

/* Bottom rounded edge refraction */
.glass-navbar:hover::after {
  bottom: -1px;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    transparent 15%,
    rgba(255, 255, 255, 0.6) 25%,
    rgba(255, 255, 255, 0.7) 50%,
    rgba(255, 255, 255, 0.6) 75%,
    transparent 85%,
    transparent 100%
  );
  opacity: 1;
  border-radius: 0 0 20px 20px;
  box-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
  mask: radial-gradient(ellipse 80% 100% at 50% 100%, black 60%, transparent 70%);
  -webkit-mask: radial-gradient(ellipse 80% 100% at 50% 100%, black 60%, transparent 70%);
}

.nav-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
}

/* Side edge refractions using nav-container pseudo-elements - Only on rounded corners */
.nav-container::before,
.nav-container::after {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  width: 3px;
  pointer-events: none;
  opacity: 0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: -1;
}

/* Left rounded edge refraction */
.glass-navbar:hover .nav-container::before {
  left: -1px;
  background: linear-gradient(
    180deg,
    transparent 0%,
    transparent 15%,
    rgba(255, 255, 255, 0.7) 25%,
    rgba(255, 255, 255, 0.8) 50%,
    rgba(255, 255, 255, 0.7) 75%,
    transparent 85%,
    transparent 100%
  );
  opacity: 1;
  border-radius: 20px 0 0 20px;
  box-shadow: 0 0 8px rgba(255, 255, 255, 0.4);
  mask: radial-gradient(ellipse 100% 80% at 0% 50%, black 60%, transparent 70%);
  -webkit-mask: radial-gradient(ellipse 100% 80% at 0% 50%, black 60%, transparent 70%);
}

/* Right rounded edge refraction */
.glass-navbar:hover .nav-container::after {
  right: -1px;
  background: linear-gradient(
    180deg,
    transparent 0%,
    transparent 15%,
    rgba(255, 255, 255, 0.6) 25%,
    rgba(255, 255, 255, 0.7) 50%,
    rgba(255, 255, 255, 0.6) 75%,
    transparent 85%,
    transparent 100%
  );
  opacity: 1;
  border-radius: 0 20px 20px 0;
  box-shadow: 0 0 6px rgba(255, 255, 255, 0.3);
  mask: radial-gradient(ellipse 100% 80% at 100% 50%, black 60%, transparent 70%);
  -webkit-mask: radial-gradient(ellipse 100% 80% at 100% 50%, black 60%, transparent 70%);
}

.nav-brand h2 {
  color: rgba(255, 255, 255, 0.95);
  font-size: 1.5rem;
  font-weight: 600;
  letter-spacing: -0.025em;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  background: linear-gradient(135deg, #ffffff, #e0e0e0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.nav-logo {
  height: 35px;
  width: auto;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
  transition: transform 0.3s ease;
  display: block;
  margin: auto;
}

.nav-logo:hover {
  transform: scale(1.05);
}

.nav-menu {
  display: flex;
  gap: 32px;
  align-items: center;
}

/* Hide mobile menu button on desktop */
.mobile-menu-btn {
  display: none;
}

/* Hide mobile menu on desktop */
.mobile-menu {
  display: none;
}

/* Hide mobile design button on desktop */
.mobile-design-btn {
  display: none;
}

.nav-link {
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  font-size: 0.95rem;
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 12px;
  position: relative;
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: rgba(255, 255, 255, 1);
}

.nav-link:active {
  transform: translateY(0);
}

/* Design Mode Button - Reddish glass style */
.design-mode-btn {
  background: rgba(139, 0, 0, 0.15);
  backdrop-filter: blur(10px) saturate(180%);
  -webkit-backdrop-filter: blur(10px) saturate(180%);
  color: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(139, 0, 0, 0.4);
  border-radius: 20px;
  padding: 15px 25px;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 
    0 8px 32px rgba(139, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    inset 0 -1px 0 rgba(139, 0, 0, 0.3);
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
}

.design-mode-btn:hover {
  background: rgba(139, 0, 0, 0.25);
  border-color: rgba(139, 0, 0, 0.6);
  backdrop-filter: blur(15px) saturate(200%);
  -webkit-backdrop-filter: blur(15px) saturate(200%);
  transform: translateY(-3px);
  box-shadow: 
    0 12px 40px rgba(139, 0, 0, 0.3),
    0 0 20px rgba(139, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    inset 0 -1px 0 rgba(139, 0, 0, 0.4);
  color: white;
}

/* Edge refraction effects for design mode button */
.design-mode-btn::before,
.design-mode-btn::after {
  position: absolute;
  content: '';
  pointer-events: none;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
}

/* Top edge refraction with red tint */
.design-mode-btn:hover::before {
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 100, 100, 0.8) 20%,
    rgba(255, 150, 150, 0.9) 50%,
    rgba(255, 100, 100, 0.8) 80%,
    transparent 100%
  );
  opacity: 1;
  transform: translateY(-1px);
  box-shadow: 0 0 10px rgba(139, 0, 0, 0.6);
}

/* Bottom edge refraction with red tint */
.design-mode-btn:hover::after {
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(139, 0, 0, 0.7) 20%,
    rgba(200, 50, 50, 0.8) 50%,
    rgba(139, 0, 0, 0.7) 80%,
    transparent 100%
  );
  opacity: 1;
  transform: translateY(1px);
  box-shadow: 0 0 8px rgba(139, 0, 0, 0.5);
}

.design-mode-btn:active {
  transform: translateY(-1px);
  backdrop-filter: blur(8px) saturate(160%);
  -webkit-backdrop-filter: blur(8px) saturate(160%);
  box-shadow: 
    0 6px 20px rgba(139, 0, 0, 0.2),
    inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Main content styling */
.main-content {
  position: relative;
  z-index: 10;
  min-height: 100vh;
  padding: 120px 20px 0px;
  color: white;
}

/* Projects Section */
.projects-section {
  position: relative;
  width: 100vw;
  height: 100vh;
  min-height: 100vh;
  padding: 80px 20px;
  padding-top: 120px;
  margin-left: calc(-50vw + 50%);
  margin-right: calc(-50vw + 50%);
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  z-index: 3;
  background: #000;
  border-top: none;
  clear: both;
  scroll-margin-top: 80px;
  overflow: hidden;
}

.section-header {
  margin-bottom: 60px;
}

/* Desktop specific - Add extra top padding for projects section */
@media (min-width: 1024px) {
  .projects-section {
    padding-top: 150px;
  }
  
  .section-header {
    margin-bottom: 80px;
    margin-top: 100px;
  }
}

.section-header h2 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 20px;
  background: linear-gradient(135deg, #ffffff, #e0e0e0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.section-header p {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.8);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.content-section {
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
  padding: 60px 0;
}

.content-section h1 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 20px;
  background: linear-gradient(135deg, #ffffff, #e0e0e0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.content-section p {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.8);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Experience Section */
#experience {
  height: 100vh;
  position: relative;
  z-index: 2;
  margin-bottom: 0;
}

/* Ensure proper section separation */
#projects {
  position: relative;
  width: 100%;
  margin-top: 0;
  padding-top: 0;
  z-index: 3;
  background: transparent;
}

/* Tech Stack Section */
#tech-stack {
  height: 100vh;
}

/* Main content layout fixes */
.main-content {
  position: relative;
  z-index: 1;
}

.main-content > section {
  position: relative;
  display: block;
  width: 100%;
}

/* Force section separation */
section#experience + section#projects {
  margin-top: 0 !important;
  position: relative;
  clear: both;
}

/* Hero Section */
.hero-section {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
}

.hero-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
  width: 100%;
  gap: 60px;
}

.hero-text {
  flex: 1;
}

.hero-text h1 {
  font-size: 4rem;
  font-weight: 700;
  margin: 0;
  line-height: 1.1;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 4px 15px rgba(0, 0, 0, 0.6);
}

.hero-text h1:nth-child(2) {
  /* Style for the "&" symbol */
  background: linear-gradient(135deg, #ffffff, #e0e0e0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 4px 15px rgba(0, 0, 0, 0.6);
}

.hero-bio {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 20px;
  line-height: 1.6;
  font-weight: 400;
  max-width: 500px;
}

.hero-bio + .hero-bio {
  margin-top: 12px;
}

.hero-image {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.profile-image {
  width: 400px;
  height: 400px;
  object-fit: cover;
  border-radius: 20px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Glass Form Styles */
.glass-form-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
}

.glass-form {
  width: 100%;
  max-width: 450px;
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(8px) saturate(150%);
  -webkit-backdrop-filter: blur(8px) saturate(150%);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    inset 0 -1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
  padding: 30px;
  z-index: 20;
  transition: all 0.3s ease;
}

.glass-form:hover {
  backdrop-filter: blur(12px) saturate(180%);
  -webkit-backdrop-filter: blur(12px) saturate(180%);
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 255, 255, 0.25);
}

/* Edge refraction effects for glass form */
.glass-form::before,
.glass-form::after {
  position: absolute;
  content: '';
  pointer-events: none;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
}

/* Top edge refraction */
.glass-form:hover::before {
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.7) 15%,
    rgba(255, 255, 255, 0.9) 50%,
    rgba(255, 255, 255, 0.7) 85%,
    transparent 100%
  );
  opacity: 1;
  transform: translateY(-1px);
  box-shadow: 0 0 12px rgba(255, 255, 255, 0.4);
  border-radius: 20px 20px 0 0;
}

/* Bottom edge refraction */
.glass-form:hover::after {
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.5) 15%,
    rgba(255, 255, 255, 0.7) 50%,
    rgba(255, 255, 255, 0.5) 85%,
    transparent 100%
  );
  opacity: 1;
  transform: translateY(1px);
  box-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
  border-radius: 0 0 20px 20px;
}

/* Edge-only refraction effects - no corner glow */
.glass-navbar,
.glass-form,
.design-mode-btn {
  position: relative;
}

.glass-form h2 {
  color: rgba(255, 255, 255, 0.95);
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 20px;
  text-align: center;
  background: linear-gradient(135deg, #ffffff, #e0e0e0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Form Status Messages */
.form-message {
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  text-align: center;
}

.form-message.success {
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.3);
  color: rgba(34, 197, 94, 0.9);
}

.form-message.error {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: rgba(239, 68, 68, 0.9);
}

.form-group {
  margin-bottom: 12px;
}

.form-group label {
  display: block;
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.85rem;
  font-weight: 500;
  margin-bottom: 4px;
  text-align: left;
}

.glass-input,
.glass-textarea {
  width: 100%;
  padding: 8px 14px;
  background: rgba(255, 255, 255, 0.04);
  backdrop-filter: blur(6px);
  -webkit-backdrop-filter: blur(6px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 10px;
  color: rgba(255, 255, 255, 0.95);
  font-size: 0.95rem;
  font-family: inherit;
  outline: none;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
  height: 38px;
}

.glass-input::placeholder,
.glass-textarea::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.glass-input:focus,
.glass-textarea:focus {
  border-color: rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: 
    0 0 0 2px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.glass-textarea {
  resize: vertical;
  min-height: 65px;
  height: 65px;
}

.glass-submit-btn {
  width: 100%;
  padding: 10px 18px;
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(6px);
  -webkit-backdrop-filter: blur(6px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 10px;
  color: white;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
}

.glass-submit-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.6),
    transparent
  );
}

.glass-submit-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transform: translateY(-2px);
  box-shadow: 
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

/* Contact Section */
.contact-section {
  height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

.contact-content {
  height: 75vh;
  padding: 60px 20px 40px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1400px;
  margin: 0 auto;
  gap: 60px;
}

.contact-image {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.conred-image {
  width: 100%;
  max-width: 550px;
  height: auto;
  border-radius: 20px;
  transition: transform 0.3s ease;
  cursor: pointer;
}

.conred-image:hover {
  transform: scale(1.05);
}

.contact-form {
  flex: 1;
}

/* Tools & Technologies Section */
.tools-section {
  padding: 80px 20px;
  position: relative;
  min-height: 100vh;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.5));
  overflow: hidden;
}

.tools-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('../assets/logos/centre.png'); /* Correct path to centre.png */
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  opacity: 0.3;
  z-index: 1;
  transform: scale(1.5); /* Make background 1.5x bigger */
}

.tools-content {
  position: relative;
  z-index: 2;
  max-width: 1400px;
  margin: 0 auto;
  text-align: center;
}

.tools-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 20px;
  background: linear-gradient(135deg, #ffffff, #e0e0e0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
}

.tools-subtitle {
  font-size: 1.3rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 60px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 40px;
  margin-top: 80px;
  position: relative;
}

.tool-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  padding: 30px 20px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px) saturate(180%);
  -webkit-backdrop-filter: blur(10px) saturate(180%);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  animation: float 6s ease-in-out infinite;
}

.tool-item:nth-child(2n) {
  animation-delay: -2s;
}

.tool-item:nth-child(3n) {
  animation-delay: -4s;
}

.tool-item:hover {
  transform: translateY(-20px) scale(1.05);
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 
    0 20px 60px rgba(0, 0, 0, 0.3),
    0 0 30px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.tool-logo {
  width: 60px;
  height: 60px;
  object-fit: contain;
  filter: brightness(0.9) contrast(1.1);
  transition: all 0.3s ease;
}

.tool-item:hover .tool-logo {
  filter: brightness(1.2) contrast(1.3) drop-shadow(0 0 20px rgba(255, 255, 255, 0.3));
  transform: scale(1.1);
}

.tool-name {
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.9rem;
  font-weight: 600;
  text-align: center;
  transition: color 0.3s ease;
}

.tool-item:hover .tool-name {
  color: rgba(255, 255, 255, 1);
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

/* Floating Animation */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-10px) rotate(1deg);
  }
  50% {
    transform: translateY(0px) rotate(0deg);
  }
  75% {
    transform: translateY(-5px) rotate(-1deg);
  }
}

/* Particle Animation */
.tools-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.3), transparent),
              radial-gradient(2px 2px at 40px 70px, rgba(255, 255, 255, 0.2), transparent),
              radial-gradient(1px 1px at 90px 40px, rgba(255, 255, 255, 0.4), transparent),
              radial-gradient(1px 1px at 130px 80px, rgba(255, 255, 255, 0.3), transparent);
  background-repeat: repeat;
  background-size: 150px 100px;
  animation: sparkle 20s linear infinite;
  z-index: 1;
}

@keyframes sparkle {
  0% {
    transform: translateY(0px);
  }
  100% {
    transform: translateY(-100px);
  }
}

/* Hide mobile elements on desktop */
@media (min-width: 769px) {
  .mobile-top-bar {
    display: none !important;
  }
  
  .mobile-logo-btn {
    display: none !important;
  }
  
  .mobile-design-btn {
    display: none !important;
  }
  
  .mobile-menu {
    display: none !important;
  }
  
  .mobile-menu-backdrop {
    display: none !important;
  }
}

/* Footer - Now integrated within contact section */
.footer {
  height: 25vh;
  background: linear-gradient(135deg, #1a0611 0%, #0d0306 100%);
  padding: 10px 0;
  margin: 0;
  position: relative;
  z-index: 10;
  border-top: 1px solid rgba(139, 0, 0, 0.3);
  display: flex;
  align-items: center;
  width: 100vw;
  margin-left: calc(-50vw + 50%);
}

.footer-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 30px;
  width: 100%;
}

/* Footer Bottom Section */
.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 40px;
  height: 100%;
}

.footer-left {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.footer-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

/* Main Footer Text (now on left) */
.footer-main-text h2 {
  color: #ffffff;
  font-family: 'Varela Round', sans-serif;
  font-size: 2.2rem;
  font-weight: 400;
  margin: 0;
  text-shadow: 0 2px 10px rgba(139, 0, 0, 0.3);
  text-align: left;
}

/* Contact Information (on right) */
.contact-info {
  text-align: right;
}

.contact-info p {
  color: rgba(255, 255, 255, 0.8);
  font-family: 'Varela Round', sans-serif;
  font-size: 1.1rem;
  margin: 8px 0;
  line-height: 1.5;
}

/* Social Media Icons (now on left) */
.social-icons {
  display: flex;
  justify-content: flex-start;
  gap: 15px;
  align-items: center;
}

.social-icon {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background: rgba(139, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(139, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  cursor: pointer;
  overflow: hidden;
  position: relative;
}

.social-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, rgba(139, 0, 0, 0.2) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.social-icon:hover::before {
  opacity: 1;
}

.social-icon:hover {
  transform: translateY(-3px) scale(1.1);
  border-color: rgba(255, 68, 68, 0.6);
  box-shadow: 0 8px 20px rgba(139, 0, 0, 0.4);
}

.social-icon img {
  width: 32px;
  height: 32px;
  object-fit: contain;
  filter: brightness(0.8) contrast(1.2);
  transition: filter 0.3s ease;
  z-index: 1;
  position: relative;
}

.social-icon:hover img {
  filter: brightness(1.2) contrast(1.3);
}

/* Resume Button */
.resume-btn {
  background: rgba(139, 0, 0, 0.15);
  backdrop-filter: blur(10px) saturate(180%);
  -webkit-backdrop-filter: blur(10px) saturate(180%);
  color: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(139, 0, 0, 0.4);
  border-radius: 15px;
  padding: 10px 20px;
  font-weight: 600;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 
    0 6px 20px rgba(139, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    inset 0 -1px 0 rgba(139, 0, 0, 0.3);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
  margin-left: 15px;
  text-decoration: none;
  display: inline-block;
}

.resume-btn:hover {
  background: rgba(139, 0, 0, 0.25);
  border-color: rgba(139, 0, 0, 0.6);
  backdrop-filter: blur(15px) saturate(200%);
  -webkit-backdrop-filter: blur(15px) saturate(200%);
  transform: translateY(-2px);
  box-shadow: 
    0 8px 25px rgba(139, 0, 0, 0.3),
    0 0 15px rgba(139, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    inset 0 -1px 0 rgba(139, 0, 0, 0.4);
  color: white;
}

/* Edge refraction effects for resume button */
.resume-btn::before,
.resume-btn::after {
  position: absolute;
  content: '';
  pointer-events: none;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
}

/* Top edge refraction with red tint */
.resume-btn:hover::before {
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 100, 100, 0.8) 25%,
    rgba(255, 150, 150, 0.9) 50%,
    rgba(255, 100, 100, 0.8) 75%,
    transparent 100%
  );
  opacity: 1;
  transform: translateY(-1px);
  box-shadow: 0 0 8px rgba(139, 0, 0, 0.5);
}

/* Bottom edge refraction with red tint */
.resume-btn:hover::after {
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(139, 0, 0, 0.7) 25%,
    rgba(200, 50, 50, 0.8) 50%,
    rgba(139, 0, 0, 0.7) 75%,
    transparent 100%
  );
  opacity: 1;
  transform: translateY(1px);
  box-shadow: 0 0 6px rgba(139, 0, 0, 0.4);
}

.resume-btn:active {
  transform: translateY(-1px);
  backdrop-filter: blur(8px) saturate(160%);
  -webkit-backdrop-filter: blur(8px) saturate(160%);
  box-shadow: 
    0 4px 15px rgba(139, 0, 0, 0.2),
    inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Mobile Small (320px) */
@media (max-width: 320px) {
  .experience-section {
    height: auto !important;
    min-height: 1200px !important;
  }
  
  .silk-background-container,
  .dark-overlay-container {
    height: 100% !important;
    min-height: 1200px !important;
  }
  
  .experience-content {
    padding: 2rem 1rem !important;
    min-height: 1200px !important;
  }
  
  .projects-section {
    padding: 40px 10px !important;
    min-height: 90vh !important;
  }
}

/* Mobile Medium (375px) */
@media (min-width: 321px) and (max-width: 375px) {
  .experience-section {
    height: auto !important;
    min-height: 1200px !important;
  }
  
  .silk-background-container,
  .dark-overlay-container {
    height: 100% !important;
    min-height: 1200px !important;
  }
  
  .experience-content {
    padding: 3rem 1.5rem !important;
    min-height: 1200px !important;
  }
  
  .projects-section {
    padding: 50px 12px !important;
    min-height: 95vh !important;
  }
}

/* Responsive Design for Mobile Large (425px and below) */
@media (max-width: 425px) {
  /* Experience Section - ensure proper containment */
  .experience-section {
    width: 100vw !important;
    max-width: 100vw !important;
    min-width: 100vw !important;
    position: relative !important;
    left: 0 !important;
    right: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    overflow: hidden !important;
    height: auto !important;
    min-height: 1200px !important;
    z-index: 2 !important;
  }

  /* Silk background container - full stretch */
  .silk-background-container {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100% !important;
    min-height: 1200px !important;
    z-index: 1 !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  /* Silk Canvas - ensure full coverage */
  .experience-section canvas,
  .silk-background-container canvas {
    width: 100vw !important;
    height: 100% !important;
    min-height: 1200px !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    object-fit: cover !important;
    object-position: center !important;
  }

  /* Dark overlay - match background */
  .dark-overlay-container {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100% !important;
    min-height: 1200px !important;
    z-index: 1.5 !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  /* Experience content - centered and contained */
  .experience-content {
    position: relative !important;
    z-index: 2 !important;
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 auto !important;
    padding: 2rem 1rem !important;
    box-sizing: border-box !important;
    min-height: 1200px !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: flex-start !important;
    align-items: center !important;
  }
}

/* Mobile Responsive Design */
@media (max-width: 768px) {
  /* Global mobile fixes */
  * {
    max-width: 100% !important;
    box-sizing: border-box !important;
  }

  html, body {
    width: 100% !important;
    overflow-x: hidden !important;
  }

  /* Experience Section Mobile - Full coverage */
  .experience-section {
    width: 100vw !important;
    min-height: 100vh !important;
    height: auto !important;
    position: relative !important;
    overflow: hidden !important;
  }

  /* Silk Background and Canvas - Dynamic height coverage */
  .silk-background-container {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100% !important;
    min-height: 100vh !important;
    z-index: 1 !important;
  }

  .experience-section canvas,
  .silk-background-container canvas {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100% !important;
    min-height: 100vh !important;
    object-fit: cover !important;
    object-position: center !important;
  }

  /* Dark overlay - match background height */
  .dark-overlay-container {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100% !important;
    min-height: 100vh !important;
    z-index: 1.5 !important;
  }

  /* MOBILE NAVBAR LAYOUT */
  .navbar-wrapper {
    display: block !important;
    position: fixed !important;
    top: 20px !important;
    left: 20px !important;
    right: 20px !important;
    transform: none !important;
    z-index: 1000 !important;
    width: calc(100% - 40px) !important;
    height: auto !important;
  }
  
  .glass-navbar {
    display: none !important;
  }

  .nav-container {
    display: none !important;
  }

  /* Hide desktop menu on mobile */
  .desktop-menu {
    display: none;
  }

  /* Hide desktop design mode button on mobile */
  .desktop-design-btn {
    display: none;
  }

  /* Hide mobile menu button (3 dots) */
  .mobile-menu-btn {
    display: none !important;
  }

  /* Hide logo on mobile */
  .nav-logo {
    display: none !important;
  }

  .mobile-menu-btn:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  .mobile-menu-btn .dot {
    width: 4px;
    height: 4px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    transition: all 0.3s ease;
  }

  .mobile-menu-btn:hover .dot {
    background: rgba(255, 255, 255, 1);
    transform: scale(1.2);
  }

  /* Mobile top bar container */
  .mobile-top-bar {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    position: relative !important;
    z-index: 1001 !important;
    gap: 15px !important;
    width: 100% !important;
    height: 50px !important;
    background: rgba(255, 255, 255, 0.05) !important;
    backdrop-filter: blur(10px) !important;
    border-radius: 15px !important;
    padding: 0 15px !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
  }

  /* Mobile logo button */
  .mobile-logo-btn {
    display: block !important;
    position: relative !important;
    padding: 10px 15px !important;
    height: 56px !important;
    width: auto !important;
    min-width: 80px !important;
    border-radius: 12px !important;
    background: transparent !important;
    border: none !important;
    backdrop-filter: none !important;
    transition: all 0.3s ease !important;
    box-shadow: none !important;
    cursor: pointer !important;
    flex-shrink: 0 !important;
  }

  .mobile-logo-btn img {
    width: auto !important;
    height: 36px !important;
    max-width: 70px !important;
    object-fit: contain !important;
    display: block !important;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3)) !important;
  }

  .mobile-logo-btn:hover {
    transform: scale(1.05) !important;
  }

  .mobile-logo-btn:hover img {
    filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.4)) !important;
  }

  /* Show mobile design mode button - red glass on right */
  .mobile-design-btn {
    display: block !important;
    position: relative !important;
    padding: 8px 16px !important;
    font-size: 12px !important;
    height: 40px !important;
    min-width: 100px !important;
    border-radius: 12px !important;
    background: rgba(139, 0, 0, 0.2) !important;
    color: white !important;
    border: 1px solid rgba(139, 0, 0, 0.4) !important;
    backdrop-filter: blur(10px) !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 4px 15px rgba(139, 0, 0, 0.3) !important;
    text-align: center !important;
    font-weight: 500 !important;
    cursor: pointer !important;
    flex-shrink: 0 !important;
  }

  .mobile-design-btn:hover {
    background: rgba(139, 0, 0, 0.3) !important;
    border-color: rgba(139, 0, 0, 0.6) !important;
    box-shadow: 0 6px 20px rgba(139, 0, 0, 0.4) !important;
  }

  /* Mobile dropdown menu - 4 rows layout */
  .mobile-menu {
    display: flex !important;
    flex-direction: column !important;
    position: absolute;
    top: 60px;
    left: 0;
    width: 200px;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(20px);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1001;
    gap: 0;
  }

  .mobile-menu.open {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }

  .mobile-menu .nav-link {
    display: block;
    width: 100%;
    padding: 12px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    font-size: 14px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
    text-align: left;
    border-radius: 0;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    text-decoration: none;
    box-sizing: border-box;
  }

  .mobile-menu .nav-link:last-child {
    border-bottom: none;
    border-radius: 0 0 12px 12px;
  }

  .mobile-menu .nav-link:first-child {
    border-radius: 12px 12px 0 0;
  }

  .mobile-menu .nav-link::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 0;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.1), transparent);
    transition: width 0.3s ease;
  }

  .mobile-menu .nav-link:hover {
    background: rgba(255, 255, 255, 0.05);
    color: rgba(255, 255, 255, 1);
    padding-left: 25px;
  }

  .mobile-menu .nav-link:hover::before {
    width: 4px;
  }

  .mobile-menu .nav-link:active {
    background: rgba(255, 255, 255, 0.08);
    transform: scale(0.98);
  }

  /* Mobile Design Mode Button - OLD STYLE REMOVED */
  /* This old mobile design button style is now commented out to prevent conflicts */

  /* Mobile Menu Backdrop */
  .mobile-menu-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(2px);
    z-index: 999;
    animation: fadeIn 0.3s ease;
  }

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  
  .main-content {
    padding: 140px 15px 0px 15px;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
  }
  
  /* Exclude experience section from main-content constraints */
  .main-content #experience {
    width: 100vw !important;
    max-width: none !important;
    margin: 0 !important;
    padding: 0 !important;
    position: relative !important;
    left: 50% !important;
    right: 50% !important;
    margin-left: -50vw !important;
    margin-right: -50vw !important;
    box-sizing: border-box !important;
  }

  /* Exclude tech-stack section from main-content constraints */
  .main-content #tech-stack {
    width: 100vw !important;
    max-width: none !important;
    margin: 0 !important;
    padding: 0 !important;
    position: relative !important;
    left: 50% !important;
    right: 50% !important;
    margin-left: -50vw !important;
    margin-right: -50vw !important;
    box-sizing: border-box !important;
  }
  
  .hero-content {
    flex-direction: column;
    gap: 40px;
    text-align: center;
    width: 100%;
    max-width: 100%;
    padding: 0 15px;
    box-sizing: border-box;
  }
  
  .hero-section {
    width: 100%;
    max-width: 100%;
    padding: 0;
    margin: 0;
  }
  
  .hero-text {
    width: 100%;
    max-width: 100%;
  }
  
  .hero-text h1 {
    font-size: 2.5rem;
    width: 100%;
  }
  
  .hero-bio {
    font-size: 1rem;
    margin-top: 15px;
    width: 100%;
    padding: 0 10px;
  }
  
  .profile-image {
    width: 280px;
    height: 280px;
  }

  /* Ensure all sections use full width on mobile */
  section {
    width: 100% !important;
    max-width: 100% !important;
    padding-left: 15px !important;
    padding-right: 15px !important;
    box-sizing: border-box !important;
  }

  /* Projects section mobile - remove background container */
  .projects-section {
    width: 100% !important;
    max-width: 100% !important;
    padding: 600px 15px 40px 15px !important;
    margin: 150px 0 0 0 !important;
    height: auto !important;
    min-height: 100vh !important;
    position: relative !important;
    z-index: 4 !important;
    background: transparent !important;
    border-top: none !important;
    margin-top: 150px !important;
    clear: both !important;
  }

  /* Projects section header mobile */
  .projects-section .section-header {
    margin-bottom: 40px !important;
    padding-top: 20px !important;
  }

  .projects-section .section-header h2 {
    font-size: 2.5rem !important;
    margin-bottom: 15px !important;
  }

  /* Tech section mobile - let TechShowcase handle its own styling */
  #tech-stack {
    width: 100vw !important;
    max-width: none !important;
    padding: 0 !important;
    margin: 0 !important;
  }
  
  .content-section h1 {
    font-size: 2rem;
  }
  
  .content-section p {
    font-size: 1rem;
  }
  

  
  .section-header h2 {
    font-size: 2.5rem;
  }
  
  .section-header p {
    font-size: 1rem;
  }
  
  .footer-main-text h2 {
    font-size: 2rem;
    text-align: center;
  }
  
  .footer-bottom {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 25px;
  }
  
  .footer-left {
    align-items: center;
  }
  
  .footer-right {
    align-items: center;
  }
  
  .contact-info {
    text-align: center;
  }
  
  .social-icons {
    justify-content: center;
    flex-wrap: wrap;
    gap: 10px;
    max-width: 100%;
    overflow: hidden;
  }
  
  .resume-btn {
    margin-left: 0;
    margin-top: 5px;
    padding: 8px 16px;
    font-size: 12px;
    white-space: nowrap;
  }

  /* Full Screen Footer for Mobile */
  .footer {
    margin: 40px 0 0 0;
    width: 100%;
    max-width: 100%;
    position: relative;
    box-sizing: border-box;
    padding: 20px 10px;
    margin-left: -50vw;
    margin-right: -50vw;
  }

  .footer-content {
    max-width: none;
    padding: 0 20px;
    width: 100%;
  }

  /* Experience Section Mobile Responsive Fix */
  .experience-section {
    min-height: 100vh !important;
    height: auto !important;
    padding: 0 !important;
    margin: 0 !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: flex-start !important;
    width: 100vw !important;
    left: 0 !important;
    right: 0 !important;
    position: relative !important;
    overflow: hidden !important;
  }

  /* Silk background mobile - force full width */
  .experience-section > div:first-child {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    width: 100vw !important;
    height: 100% !important;
    min-height: 100vh !important;
    z-index: 1 !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  /* Silk Canvas element - force full width */
  .experience-section canvas {
    width: 100vw !important;
    height: 100vh !important;
    min-width: 100vw !important;
    min-height: 100vh !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  /* Dark overlay mobile - force full width */
  .experience-section > div:nth-child(2) {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    width: 100vw !important;
    height: 100% !important;
    min-height: 100vh !important;
    z-index: 1.5 !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  /* Main content container mobile */
  .experience-section > div:last-child {
    padding: 40px 15px !important;
    height: auto !important;
    min-height: 100vh !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: flex-start !important;
    align-items: stretch !important;
    position: relative !important;
    z-index: 2 !important;
    width: 100% !important;
    max-width: 100vw !important;
    box-sizing: border-box !important;
    margin: 0 !important;
  }

  /* Experience title mobile */
  .experience-section h2 {
    font-size: 2.5rem !important;
    margin-bottom: 3rem !important;
    text-align: center !important;
    width: 100% !important;
    margin-top: 2rem !important;
  }

  /* Experience companies container mobile */
  .experience-section > div > div[style*="gap: 8rem"] {
    flex-direction: column !important;
    gap: 4rem !important;
    align-items: center !important;
    width: 100% !important;
    max-width: 100% !important;
    padding: 0 15px !important;
    box-sizing: border-box !important;
    margin: 0 auto !important;
  }

  /* Individual company cards mobile */
  .experience-section > div > div > div {
    max-width: 320px !important;
    width: 100% !important;
    margin: 0 auto !important;
    padding: 0 10px !important;
    box-sizing: border-box !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
  }

  /* Company images mobile */
  .experience-section img {
    width: 120px !important;
    height: 140px !important;
    margin-bottom: 1rem !important;
  }

  /* Company names mobile */
  .experience-section h3 {
    font-size: 1.3rem !important;
    margin-bottom: 0.5rem !important;
    text-align: center !important;
  }

  /* Job titles mobile */
  .experience-section p[style*="color: #FFB6C1"] {
    font-size: 1.1rem !important;
    margin-bottom: 0.5rem !important;
    text-align: center !important;
  }

  /* Dates mobile */
  .experience-section p[style*="opacity: 0.7"] {
    font-size: 1rem !important;
    margin-bottom: 1.5rem !important;
    text-align: center !important;
  }

  /* Job descriptions mobile */
  .experience-section p[style*="textAlign: 'justify'"] {
    font-size: 1rem !important;
    line-height: 1.6 !important;
    text-align: left !important;
    padding: 0 !important;
    width: 100% !important;
    max-width: 100% !important;
  }
}

/* Responsive Design for Contact Section */
@media (max-width: 768px) {
  .contact-content {
    flex-direction: column;
    gap: 40px;
    align-items: center;
    text-align: center;
  }
  
  .contact-image {
    order: 1;
    width: 100%;
    max-width: 100%;
  }
  
  .conred-image {
    max-width: 350px;
    width: 90%;
    margin: 0 auto;
  }
  
  .contact-form {
    order: 2;
    width: 100%;
    max-width: 100%;
  }
  
  .glass-form-container {
    padding: 15px 5px;
    align-items: stretch;
  }
  
  .glass-form {
    width: 100%;
    max-width: 100%;
    margin: 0;
    padding: 18px 22px;
    border-radius: 16px;
    box-sizing: border-box;
  }
  
  .glass-form h2 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    text-align: center;
  }
  
  .form-group {
    margin-bottom: 10px;
  }
  
  .glass-input,
  .glass-textarea {
    padding: 8px 12px;
    font-size: 16px; /* Prevents zoom on iOS */
    border-radius: 8px;
    height: 36px;
  }
  
  .glass-textarea {
    min-height: 60px;
    height: 60px;
  }
  
  .glass-submit-btn {
    padding: 14px 20px;
    font-size: 16px;
    border-radius: 10px;
    margin-top: 8px;
  }
  
  .contact-section {
    height: auto;
    min-height: 100vh;
    flex-direction: column;
  }
  
  .contact-content {
    height: auto;
    min-height: 75vh;
    padding: 30px 15px 20px 15px;
    flex-direction: column;
    gap: 40px;
    align-items: center;
  }
  
  .contact-image {
    order: 1;
    width: 100%;
    max-width: 400px;
    margin-bottom: 20px;
  }
  
  .contact-form {
    order: 2;
    width: 100%;
    max-width: 400px;
  }
  
  .conred-image {
    max-width: 100%;
    width: 350px;
    height: auto;
  }
  
  .footer {
    height: auto;
    min-height: 25vh;
    padding: 20px 15px;
    margin: 40px 0 0 0;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    overflow: hidden;
  }
  
  .footer-content {
    padding: 0 10px;
    max-width: 400px;
    margin: 0 auto;
    width: 100%;
    box-sizing: border-box;
  }
  
  .footer-bottom {
    flex-direction: column;
    align-items: center;
    gap: 20px;
    text-align: center;
  }
  
  .footer-left {
    order: 1;
    align-items: center;
  }
  
  .footer-right {
    order: 2;
    align-items: center;
  }
  
  .footer-main-text h2 {
    font-size: 1.6rem;
    text-align: center;
    margin: 0;
  }
  
  .contact-info {
    text-align: center;
  }

  /* Mobile Tools Section - HUGE Image & Floating Logos */
  .tools-section {
    padding: 60px 10px;
    min-height: 120vh; /* Even taller on mobile */
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.3));
  }

  .tools-background {
    background-image: url('../assets/logos/centre.png'); /* Correct path to centre.png */
    background-size: cover;
    background-position: center;
    background-attachment: scroll; /* Better for mobile */
    opacity: 0.5; /* More visible on mobile */
    transform: scale(5.0); /* Make background MUCH BIGGER - increased from 3.6 to 5.0 */
  }

  .tools-content {
    padding: 0 15px;
  }

  .tools-title {
    font-size: 2rem; /* Normal size text */
    margin-bottom: 15px;
    text-shadow: 0 4px 30px rgba(0, 0, 0, 0.8);
  }

  .tools-subtitle {
    font-size: 1rem; /* Normal size text */
    margin-bottom: 50px;
    color: rgba(255, 255, 255, 0.9);
  }

  .tools-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 25px;
    margin-top: 60px;
  }

  .tool-item {
    padding: 25px 15px;
    border-radius: 18px;
    animation: floatMobile 4s ease-in-out infinite;
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(15px) saturate(200%);
    -webkit-backdrop-filter: blur(15px) saturate(200%);
    border: 1px solid rgba(255, 255, 255, 0.15);
  }

  .tool-item:hover {
    transform: translateY(-15px) scale(1.08);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 
      0 15px 40px rgba(0, 0, 0, 0.4),
      0 0 25px rgba(255, 255, 255, 0.2);
  }

  .tool-logo {
    width: 50px;
    height: 50px;
  }

  .tool-item:hover .tool-logo {
    transform: scale(1.15);
    filter: brightness(1.3) contrast(1.4) drop-shadow(0 0 15px rgba(255, 255, 255, 0.4));
  }

  .tool-name {
    font-size: 0.8rem;
    font-weight: 700;
  }

  /* Enhanced Mobile Float Animation */
  @keyframes floatMobile {
    0%, 100% {
      transform: translateY(0px) rotate(0deg);
    }
    25% {
      transform: translateY(-8px) rotate(2deg);
    }
    50% {
      transform: translateY(0px) rotate(0deg);
    }
    75% {
      transform: translateY(-4px) rotate(-2deg);
    }
  }

  /* Mobile Particle Effect */
  .tools-section::before {
    background-size: 100px 80px;
    animation: sparkleMobile 15s linear infinite;
  }

  @keyframes sparkleMobile {
    0% {
      transform: translateY(0px);
    }
    100% {
      transform: translateY(-80px);
    }
  }
}

/* Extra small mobile devices - Contact section specific */
@media (max-width: 480px) {
  .contact-content {
    padding: 20px 10px 15px 10px;
    gap: 30px;
  }
  
  .contact-image {
    max-width: 320px;
  }
  
  .conred-image {
    width: 300px;
    max-width: 100%;
  }
  
  .contact-form {
    max-width: 320px;
  }
  
  .footer {
    margin-top: 30px;
    padding: 15px 10px;
    width: 100%;
    max-width: 100%;
  }
  
  .footer-content {
    padding: 0 10px;
    max-width: 320px;
    width: 100%;
  }
  
  .social-icon {
    width: 35px;
    height: 35px;
  }
  
  .social-icon img {
    width: 25px;
    height: 25px;
  }
}

@media (max-width: 375px) {
  .contact-content {
    padding: 15px 8px 10px 8px;
    gap: 25px;
  }
  
  .contact-image {
    max-width: 280px;
  }
  
  .conred-image {
    width: 260px;
  }
  
  .contact-form {
    max-width: 280px;
  }
  
  .footer-content {
    padding: 0 8px;
    max-width: 280px;
    width: 100%;
  }
  
  .social-icon {
    width: 32px;
    height: 32px;
  }
  
  .social-icon img {
    width: 22px;
    height: 22px;
  }
}

@media (max-width: 320px) {
  .contact-content {
    padding: 12px 6px 8px 6px;
    gap: 20px;
  }
  
  .contact-image {
    max-width: 250px;
  }
  
  .conred-image {
    width: 230px;
  }
  
  .contact-form {
    max-width: 250px;
  }
  
  .footer-content {
    padding: 0 6px;
    max-width: 250px;
    width: 100%;
  }
  
  .social-icon {
    width: 30px;
    height: 30px;
  }
  
  .social-icon img {
    width: 20px;
    height: 20px;
  }
}


