function t(e){"sticky"===getComputedStyle(e).position&&(e.style.setProperty("position","static"),e.dataset.sticky="true"),e.offsetParent&&t(e.offsetParent)}function e(t){"true"===t?.dataset?.sticky&&(t.style.removeProperty("position"),delete t.dataset.sticky),t.offsetParent&&e(t.offsetParent)}function i(t,e=0){const s=e+t.offsetTop;return t.offsetParent?i(t.offsetParent,s):s}function s(t,e=0){const i=e+t.offsetLeft;return t.offsetParent?s(t.offsetParent,i):i}function n(t,e=0){const i=e+t.scrollTop;return t.offsetParent?n(t.offsetParent,i):i+window.scrollY}function o(t,e=0){const i=e+t.scrollLeft;return t.offsetParent?o(t.offsetParent,i):i+window.scrollX}var r=class{element;options;align;rect={};wrapperResizeObserver;resizeObserver;constructor(t,{align:e=["start"],ignoreSticky:i=!0,ignoreTransform:s=!1}={}){this.element=t,this.options={align:e,ignoreSticky:i,ignoreTransform:s},this.align=[e].flat(),this.wrapperResizeObserver=new ResizeObserver(this.onWrapperResize),this.wrapperResizeObserver.observe(document.body),this.onWrapperResize(),this.resizeObserver=new ResizeObserver(this.onResize),this.resizeObserver.observe(this.element),this.setRect({width:this.element.offsetWidth,height:this.element.offsetHeight})}destroy(){this.wrapperResizeObserver.disconnect(),this.resizeObserver.disconnect()}setRect({top:t,left:e,width:i,height:s,element:n}={}){t=t??this.rect.top,e=e??this.rect.left,i=i??this.rect.width,s=s??this.rect.height,n=n??this.rect.element,t===this.rect.top&&e===this.rect.left&&i===this.rect.width&&s===this.rect.height&&n===this.rect.element||(this.rect.top=t,this.rect.y=t,this.rect.width=i,this.rect.height=s,this.rect.left=e,this.rect.x=e,this.rect.bottom=t+s,this.rect.right=e+i)}onWrapperResize=()=>{let r,h;if(this.options.ignoreSticky&&t(this.element),this.options.ignoreTransform)r=i(this.element),h=s(this.element);else{const t=this.element.getBoundingClientRect();r=t.top+n(this.element),h=t.left+o(this.element)}this.options.ignoreSticky&&e(this.element),this.setRect({top:r,left:h})};onResize=([t])=>{if(!t?.borderBoxSize[0])return;const e=t.borderBoxSize[0].inlineSize,i=t.borderBoxSize[0].blockSize;this.setRect({width:e,height:i})}},h=0;function a(){return h++}globalThis.Snap=class{constructor(t,{type:e="proximity",lerp:i,easing:s,duration:n,distanceThreshold:o="50%",debounce:r=500,onSnapStart:h,onSnapComplete:a}={}){this.lenis=t,this.options={type:e,lerp:i,easing:s,duration:n,distanceThreshold:o,debounce:r,onSnapStart:h,onSnapComplete:a},this.onWindowResize(),window.addEventListener("resize",this.onWindowResize,!1),this.onSnapDebounced=function(t,e){let i;return function(...s){let n=this;clearTimeout(i),i=setTimeout((()=>{i=void 0,t.apply(n,s)}),e)}}(this.onSnap,this.options.debounce),this.lenis.on("virtual-scroll",this.onSnapDebounced)}options;elements=new Map;snaps=new Map;viewport={width:window.innerWidth,height:window.innerHeight};isStopped=!1;onSnapDebounced;destroy(){this.lenis.off("virtual-scroll",this.onSnapDebounced),window.removeEventListener("resize",this.onWindowResize,!1),this.elements.forEach((t=>t.destroy()))}start(){this.isStopped=!1}stop(){this.isStopped=!0}add(t,e={}){const i=a();return this.snaps.set(i,{value:t,userData:e}),()=>this.snaps.delete(i)}addElement(t,e={}){const i=a();return this.elements.set(i,new r(t,e)),()=>this.elements.delete(i)}onWindowResize=()=>{this.viewport.width=window.innerWidth,this.viewport.height=window.innerHeight};onSnap=()=>{let{scroll:t,isHorizontal:e}=this.lenis;t=Math.ceil(this.lenis.scroll);let i=[...this.snaps.values()];if(this.elements.forEach((({rect:t,align:s})=>{let n;s.forEach((s=>{"start"===s?n=t.top:"center"===s?n=e?t.left+t.width/2-this.viewport.width/2:t.top+t.height/2-this.viewport.height/2:"end"===s&&(n=e?t.left+t.width-this.viewport.width:t.top+t.height-this.viewport.height),"number"==typeof n&&i.push({value:Math.ceil(n),userData:{}})}))})),i=i.sort(((t,e)=>Math.abs(t.value)-Math.abs(e.value))),0===i.length)return;let s=i.findLast((({value:e})=>e<=t));void 0===s&&(s=i[0]);const n=Math.abs(t-s.value);let o=i.find((({value:e})=>e>=t));void 0===o&&(o=i[i.length-1]);const r=n<Math.abs(t-o.value)?s:o,h=Math.abs(t-r.value);let a;const l=e?"width":"height";a="string"==typeof this.options.distanceThreshold&&this.options.distanceThreshold.endsWith("%")?Number(this.options.distanceThreshold.replace("%",""))/100*this.viewport[l]:"number"==typeof this.options.distanceThreshold?this.options.distanceThreshold:this.viewport[l],("mandatory"===this.options.type||"proximity"===this.options.type&&h<=a)&&this.lenis.scrollTo(r.value,{lerp:this.options.lerp,easing:this.options.easing,duration:this.options.duration,userData:{initiator:"snap"},onStart:()=>{this.options.onSnapStart?.(r)},onComplete:()=>{this.options.onSnapComplete?.(r)}})}};//# sourceMappingURL=lenis-snap.min.js.map