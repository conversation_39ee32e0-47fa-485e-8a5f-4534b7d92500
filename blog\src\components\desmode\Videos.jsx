import React, { useRef, useState } from 'react';
import v1 from '../../assets/desmode/v1.mp4';
import v2 from '../../assets/desmode/v2.mp4';
import v3 from '../../assets/desmode/v3.mp4';
import v4 from '../../assets/desmode/v4.mp4';
import v5 from '../../assets/desmode/v5.mp4';
import v6 from '../../assets/desmode/v6.mp4';
import h1 from '../../assets/desmode/h1.mp4';
import h2 from '../../assets/desmode/h2.mp4';

const Videos = () => {
  const [videoStates, setVideoStates] = useState({
    v1: { isPlaying: false, isMuted: true },
    v2: { isPlaying: false, isMuted: true },
    v3: { isPlaying: false, isMuted: true },
    v4: { isPlaying: false, isMuted: true },
    v5: { isPlaying: false, isMuted: true },
    v6: { isPlaying: false, isMuted: true },
    h1: { isPlaying: false, isMuted: true },
    h2: { isPlaying: false, isMuted: true }
  });

  const videoRefs = {
    v1: useRef(null),
    v2: useRef(null),
    v3: useRef(null),
    v4: useRef(null),
    v5: useRef(null),
    v6: useRef(null),
    h1: useRef(null),
    h2: useRef(null)
  };

  const handleMouseEnter = (videoKey) => {
    const video = videoRefs[videoKey].current;
    if (video) {
      video.play();
      setVideoStates(prev => ({
        ...prev,
        [videoKey]: { ...prev[videoKey], isPlaying: true }
      }));
    }
  };

  const handleMouseLeave = (videoKey) => {
    const video = videoRefs[videoKey].current;
    if (video) {
      video.pause();
      video.currentTime = 0;
      setVideoStates(prev => ({
        ...prev,
        [videoKey]: { ...prev[videoKey], isPlaying: false }
      }));
    }
  };

  const togglePlayPause = (videoKey, e) => {
    e.stopPropagation();
    const video = videoRefs[videoKey].current;
    if (video) {
      if (videoStates[videoKey].isPlaying) {
        video.pause();
      } else {
        video.play();
      }
      setVideoStates(prev => ({
        ...prev,
        [videoKey]: { ...prev[videoKey], isPlaying: !prev[videoKey].isPlaying }
      }));
    }
  };

  const toggleMute = (videoKey, e) => {
    e.stopPropagation();
    const video = videoRefs[videoKey].current;
    if (video) {
      video.muted = !video.muted;
      setVideoStates(prev => ({
        ...prev,
        [videoKey]: { ...prev[videoKey], isMuted: !prev[videoKey].isMuted }
      }));
    }
  };

  const reelVideos = [
    { key: 'v1', src: v1, alt: 'Reel Video 1' },
    { key: 'v2', src: v2, alt: 'Reel Video 2' },
    { key: 'v3', src: v3, alt: 'Reel Video 3' },
    { key: 'v4', src: v4, alt: 'Reel Video 4' },
    { key: 'v5', src: v5, alt: 'Reel Video 5' },
    { key: 'v6', src: v6, alt: 'Reel Video 6' }
  ];

  const youtubeVideos = [
    { key: 'h1', src: h1, alt: 'YouTube Video 1' },
    { key: 'h2', src: h2, alt: 'YouTube Video 2' }
  ];

  const renderVideoItem = (video, className = '') => (
    <div 
      key={video.key}
      className={`video-item-hover ${className}`}
      onMouseEnter={() => handleMouseEnter(video.key)}
      onMouseLeave={() => handleMouseLeave(video.key)}
    >
      <div className="video-container">
        <video
          ref={videoRefs[video.key]}
          src={video.src}
          muted={videoStates[video.key].isMuted}
          loop
          playsInline
          className="hover-video"
        />
        
        <div className="video-controls">
          <button
            className="control-btn play-pause-btn"
            onClick={(e) => togglePlayPause(video.key, e)}
            aria-label={videoStates[video.key].isPlaying ? 'Pause' : 'Play'}
          >
            {videoStates[video.key].isPlaying ? '⏸' : '▶'}
          </button>
          
          <button
            className="control-btn mute-btn"
            onClick={(e) => toggleMute(video.key, e)}
            aria-label={videoStates[video.key].isMuted ? 'Unmute' : 'Mute'}
          >
            {videoStates[video.key].isMuted ? '🔇' : '🔊'}
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="videos-section">
      <div className="videos-main-grid">
        {/* Desktop Layout */}
        <div className="reel-videos-left">
          {reelVideos.map(video => renderVideoItem(video, 'reel-video'))}
        </div>
        
        <div className="youtube-videos-right">
          {youtubeVideos.map(video => renderVideoItem(video, 'youtube-video'))}
          
          {/* Footer Box */}
          <div className="videos-footer-box">
            <div className="footer-quote">
              <div className="quote-line-1">
                <span className="quote-word-1">I</span>{' '}
                <span className="quote-word-2">WAS</span>
              </div>
              <div className="quote-line-2">
                <span className="quote-word-3">CREATED</span>
              </div>
              <div className="quote-line-3">
                <span className="quote-word-4">TO</span>{' '}
                <span className="quote-word-5">CREATE</span>
              </div>
            </div>
            <div className="footer-buttons">
              <button className="footer-btn">Resume</button>
              <button className="footer-btn">Videos</button>
              <button className="footer-btn">Designs</button>
            </div>
          </div>
        </div>
        
        {/* Mobile Layout - Alternating Pattern */}
        <div className="videos-mobile-container">
          {/* 2 reel videos */}
          {renderVideoItem(reelVideos[0], 'reel-video')}
          {renderVideoItem(reelVideos[1], 'reel-video')}
          
          {/* 1 YouTube video */}
          {renderVideoItem(youtubeVideos[0], 'youtube-video')}
          
          {/* 2 reel videos */}
          {renderVideoItem(reelVideos[2], 'reel-video')}
          {renderVideoItem(reelVideos[3], 'reel-video')}
          
          {/* 1 YouTube video */}
          {renderVideoItem(youtubeVideos[1], 'youtube-video')}
          
          {/* 2 reel videos */}
          {renderVideoItem(reelVideos[4], 'reel-video')}
          {renderVideoItem(reelVideos[5], 'reel-video')}
          
          {/* Mobile Footer */}
          <div className="videos-footer-box mobile-footer">
            <div className="footer-quote">
              <div className="quote-line-1">
                <span className="quote-word-1">I</span>{' '}
                <span className="quote-word-2">WAS</span>
              </div>
              <div className="quote-line-2">
                <span className="quote-word-3">CREATED</span>
              </div>
              <div className="quote-line-3">
                <span className="quote-word-4">TO</span>{' '}
                <span className="quote-word-5">CREATE</span>
              </div>
            </div>
            <div className="footer-buttons">
              <button className="footer-btn">Resume</button>
              <button className="footer-btn">Videos</button>
              <button className="footer-btn">Designs</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Videos;
