import React from 'react';
import CircularGallery from './CircularGallery';

// Import your 8 images
import A1 from '../../assets/desmode/A1.jpg';
import A2 from '../../assets/desmode/A2.jpg';
import A3 from '../../assets/desmode/A3.jpg';
import A4 from '../../assets/desmode/A4.jpg';
import A5 from '../../assets/desmode/A5.jpg';
import A6 from '../../assets/desmode/A6.jpg';
import A7 from '../../assets/desmode/A7.jpg';
import A8 from '../../assets/desmode/A8.jpg';

const Home = () => {
  // Define your 8 images in order A1-A8 with alternating aspect ratios
  const galleryItems = [
    { image: A1, text: "Portfolio 01", aspectRatio: 0.8 }, // 4:5 ratio
    { image: A2, text: "Portfolio 02", aspectRatio: 1.78 }, // 16:9 ratio
    { image: A3, text: "Portfolio 03", aspectRatio: 0.8 }, // 4:5 ratio
    { image: A4, text: "Portfolio 04", aspectRatio: 1.78 }, // 16:9 ratio
    { image: A5, text: "Portfolio 05", aspectRatio: 0.8 }, // 4:5 ratio
    { image: A6, text: "Portfolio 06", aspectRatio: 1.78 }, // 16:9 ratio
    { image: A7, text: "Portfolio 07", aspectRatio: 0.8 }, // 4:5 ratio
    { image: A8, text: "Portfolio 08", aspectRatio: 1.78 }, // 16:9 ratio
  ];

  return (
    <div className="home-section">
      <div className="home-container">
        <CircularGallery
          items={galleryItems}
          bend={1.5}
          textColor="#000000"
          borderRadius={0.02}
          font="bold 28px 'Alta Regular'"
          scrollSpeed={2}
          scrollEase={0.12}
        />
      </div>
    </div>
  );
};

export default Home;
