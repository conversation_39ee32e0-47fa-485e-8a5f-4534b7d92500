/* ShinyText Component Styles */
.shiny-text {
  color: rgba(200, 200, 200, 0.9);
  background: linear-gradient(135deg, #e0e0e0, #ffffff, #c0c0c0);
  background-clip: text;
  -webkit-background-clip: text;
  display: inline-block;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 2px 8px rgba(255, 255, 255, 0.1);
}

.animate-shine {
  animation: shine 5s linear infinite;
}

@keyframes shine {
  0% {
    background-position: 100%;
  }
  100% {
    background-position: -100%;
  }
}
