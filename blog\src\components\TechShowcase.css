/* Tech Showcase - <PERSON> Inspired with <PERSON><PERSON> Theme */
.tech-showcase {
  position: relative;
  width: 100vw;
  min-height: 100vh;
  background: radial-gradient(circle at center, #2d0a1f 0%, #1a0611 50%, #0d0306 100%);
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  margin-left: calc(-50vw + 50%);
  margin-right: calc(-50vw + 50%);
  scroll-margin-top: 80px;
}

/* Tools Title */
.tools-title {
  position: absolute;
  top: 40px;
  left: 63%;
  transform: translateX(-50%);
  width: 600px;
  height: 100px;
  z-index: 20;
}

.showcase-container {
  position: relative;
  width: 100%;
  max-width: 1200px;
  height: 80vh;
  min-height: 600px;
}

/* Center Character (Doctor <PERSON> Style) */
.center-character {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
}

.character-glow {
  position: relative;
}

.character-glow::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 700px; /* Larger glow to match larger desktop image */
  height: 700px; /* Larger glow to match larger desktop image */
  background: radial-gradient(circle, rgba(255, 0, 0, 0.3) 0%, rgba(139, 0, 0, 0.2) 40%, transparent 70%);
  filter: blur(20px);
  z-index: -1;
}

.center-image {
  width: 600px; /* Larger on desktop - increased from 400px to 600px */
  height: 600px; /* Larger on desktop - increased from 400px to 600px */
  object-fit: cover;
}

/* Mystical Circles - REMOVED */

/* Floating Tech Logos */
.floating-logo {
  position: absolute;
  z-index: 5;
  transition: transform 0.3s ease;
  animation: float-logo 6s ease-in-out infinite;
  filter: drop-shadow(0 4px 8px rgba(139, 0, 0, 0.3));
}

.floating-logo img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.floating-logo:hover {
  transform: scale(1.2) !important;
  filter: drop-shadow(0 8px 16px rgba(139, 0, 0, 0.6)) brightness(1.2);
  z-index: 15;
}

.floating-logo:hover img {
  border: 2px solid rgba(139, 0, 0, 0.6);
  box-shadow: 0 0 20px rgba(139, 0, 0, 0.4);
}

/* Magical Particles */
.particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.particle {
  position: absolute;
  width: 3px;
  height: 3px;
  background: radial-gradient(circle, rgba(139, 0, 0, 0.8), rgba(85, 0, 0, 0.4));
  border-radius: 50%;
  animation: particle-float 5s ease-in-out infinite;
}

/* Animations */
@keyframes mystical-glow {
  0% {
    box-shadow: 0 0 20px rgba(139, 0, 0, 0.3);
  }
  100% {
    box-shadow: 0 0 40px rgba(139, 0, 0, 0.6), 0 0 80px rgba(85, 0, 0, 0.3);
  }
}

@keyframes character-float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes float-logo {
  0%, 100% {
    transform: translateY(0px);
  }
  33% {
    transform: translateY(-8px) rotate(2deg);
  }
  66% {
    transform: translateY(4px) rotate(-1deg);
  }
}

@keyframes particle-float {
  0% {
    opacity: 0;
    transform: translateY(0px) scale(0);
  }
  20% {
    opacity: 1;
    transform: translateY(-20px) scale(1);
  }
  80% {
    opacity: 1;
    transform: translateY(-80px) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateY(-100px) scale(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  /* Stretch frame to full screen on mobile but keep it in its section */
  .tech-showcase {
    position: relative !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    min-height: 100vh !important;
    max-width: 100vw !important;
    padding: 0 !important;
    margin: 0 !important;
    margin-left: calc(-50vw + 50%) !important;
    margin-right: calc(-50vw + 50%) !important;
    overflow: hidden !important;
    box-sizing: border-box !important;
  }
  
  /* Mobile title - minimize and center align */
  .tools-title {
    position: absolute;
    top: 10px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    width: 100vw !important;
    height: auto !important;
    z-index: 30;
    text-align: center;
    padding: 0 10px;
    box-sizing: border-box;
  }
  
  /* Increase TextPressure font size on mobile */
  .tools-title > div {
    font-size: 48px !important;
    width: 100% !important;
    text-align: center !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
  }
  
  .tools-title canvas,
  .tools-title > div > * {
    max-width: 100% !important;
    font-size: 48px !important;
    text-align: center !important;
  }
  
  /* Showcase container full screen */
  .showcase-container {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    min-height: 100vh !important;
    max-width: 100vw !important;
    padding: 0 !important;
    margin: 0 !important;
    box-sizing: border-box !important;
  }
  
  /* Center character - stretch centre.png to full max width */
  .center-character {
    position: absolute;
    bottom: 0 !important;
    left: 0 !important;
    transform: none !important;
    z-index: 5 !important;
    width: 100vw !important;
    height: auto !important;
    display: flex;
    justify-content: center;
    align-items: flex-end;
  }
  
  .character-glow {
    position: relative;
    width: 100vw !important;
    height: auto !important;
    display: flex;
    justify-content: center;
  }
  
  .character-glow::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100vw !important;
    height: 100vh !important;
    background: radial-gradient(circle, rgba(255, 0, 0, 0.2) 0%, rgba(139, 0, 0, 0.1) 40%, transparent 70%);
    filter: blur(30px);
    z-index: -1;
  }
  
  /* Stretch centre.png to full max width */
  .center-image {
    width: 100vw !important;
    height: auto !important;
    max-width: 100vw !important;
    min-width: 100vw !important;
    object-fit: cover !important;
    object-position: center bottom !important;
  }
  
  /* Floating logos above the center image - 5-4-5 arrangement */
  .floating-logo {
    position: absolute !important;
    z-index: 20 !important;
    width: 35px !important;
    height: 35px !important;
  }
  
  /* First row - 5 logos */
  .floating-logo:nth-child(1) { left: 15% !important; top: 15% !important; }
  .floating-logo:nth-child(2) { left: 27% !important; top: 15% !important; }
  .floating-logo:nth-child(3) { left: 39% !important; top: 15% !important; }
  .floating-logo:nth-child(4) { left: 51% !important; top: 15% !important; }
  .floating-logo:nth-child(5) { left: 63% !important; top: 15% !important; }
  
  /* Second row - 4 logos (centered) */
  .floating-logo:nth-child(6) { left: 21% !important; top: 25% !important; }
  .floating-logo:nth-child(7) { left: 33% !important; top: 25% !important; }
  .floating-logo:nth-child(8) { left: 45% !important; top: 25% !important; }
  .floating-logo:nth-child(9) { left: 57% !important; top: 25% !important; }
  
  /* Third row - 5 logos */
  .floating-logo:nth-child(10) { left: 15% !important; top: 35% !important; }
  .floating-logo:nth-child(11) { left: 27% !important; top: 35% !important; }
  .floating-logo:nth-child(12) { left: 39% !important; top: 35% !important; }
  .floating-logo:nth-child(13) { left: 51% !important; top: 35% !important; }
  .floating-logo:nth-child(14) { left: 63% !important; top: 35% !important; }
}

@media (max-width: 480px) {
  /* Keep same relative positioning for smaller devices - not fixed */
  .tech-showcase {
    position: relative !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    margin: 0 !important;
    margin-left: calc(-50vw + 50%) !important;
    margin-right: calc(-50vw + 50%) !important;
    box-sizing: border-box !important;
  }
  
  /* Title size for very small screens */
  .tools-title > div {
    font-size: 40px !important;
  }
  
  /* Full width center image */
  .center-image {
    width: 100vw !important;
    height: auto !important;
    max-width: 100vw !important;
    min-width: 100vw !important;
    object-fit: cover !important;
    object-position: center bottom !important;
  }
  
  /* Smaller logos on small screens */
  .floating-logo {
    width: 30px !important;
    height: 30px !important;
  }
  
  /* Maintain 5-4-5 arrangement with adjusted spacing for smaller screens */
  /* First row - 5 logos */
  .floating-logo:nth-child(1) { left: 12% !important; top: 15% !important; }
  .floating-logo:nth-child(2) { left: 34% !important; top: 10% !important; }
  .floating-logo:nth-child(3) { left: 46% !important; top: 20% !important; }
  .floating-logo:nth-child(4) { left: 68% !important; top: 10% !important; }
  .floating-logo:nth-child(5) { left: 80% !important; top: 15% !important; }
  
  /* Second row - 4 logos (centered) */
  .floating-logo:nth-child(6) { left: 18% !important; top: 25% !important; }
  .floating-logo:nth-child(7) { left: 40% !important; top: 32% !important; }
  .floating-logo:nth-child(8) { left: 12% !important; top: 35% !important; }
  .floating-logo:nth-child(9) { left: 64% !important; top: 25% !important; }
  .floating-logo:nth-child(10) { left: 82% !important; top: 35% !important; }
  
  /* Third row - 5 logos */
  
  .floating-logo:nth-child(11) { left: 14% !important; top: 55% !important; }
  .floating-logo:nth-child(12) { left: 20% !important; top: 45% !important; }
  .floating-logo:nth-child(13) { left: 68% !important; top: 45% !important; }
  .floating-logo:nth-child(14) { left: 80% !important; top: 55% !important; }
}
