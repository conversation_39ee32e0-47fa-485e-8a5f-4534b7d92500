import React from 'react';
import ScrollStack, { ScrollStackItem } from './ScrollStack';

const Works = () => {
  const workItems = [
    {
      id: 1,
      title: "UI/UX Developer and Branding",
      company: "SOULFUL AI",
      description: "An Emerging Start-up by students.",
      type: "fill" // black fill
    },
    {
      id: 2,
      title: "Banner and Logo Designs",
      company: "Various Colleges",
      description: "Logo Creation for start ups and clubs",
      type: "stroke" // black stroke
    },
    {
      id: 3,
      title: "Posters and Designs",
      company: "Ariyatra Tours and Travels",
      description: "Coimbatore",
      type: "fill" // black fill
    },
    {
      id: 4,
      title: "Media Manager",
      company: "Entrepreneurship Development Cell",
      description: "Sri Eshwar College of Engineering",
      type: "stroke" // black stroke
    },
    {
      id: 5,
      title: "Content Manager",
      company: "Program Professor",
      description: "A Youtube Channel to learn JAVA programming",
      type: "fill" // black fill
    }
  ];

  return (
    <div className="w-full h-screen bg-gray-100">
      <ScrollStack
        itemDistance={120}
        itemScale={0.04}
        itemStackDistance={40}
        stackPosition="25%"
        scaleEndPosition="15%"
        baseScale={0.8}
      >
        {workItems.map((item) => (
          <ScrollStackItem
            key={item.id}
            itemClassName={`${item.type === 'fill' ? 'bg-black text-white' : 'bg-white text-black'} shadow-2xl border-2 border-gray-800`}
          >
            <div className="h-full flex flex-col justify-center">
              <div className="mb-4">
                <h2 className="text-2xl font-bold mb-2 leading-tight uppercase tracking-wide">
                  {item.title}
                </h2>
                <h3 className="text-lg font-semibold mb-1 opacity-80 uppercase">
                  {item.company}
                </h3>
                <p className="text-sm opacity-60 mb-3 uppercase">
                  {item.description}
                </p>
              </div>
            </div>
          </ScrollStackItem>
        ))}
      </ScrollStack>
    </div>
  );
};

export default Works;


