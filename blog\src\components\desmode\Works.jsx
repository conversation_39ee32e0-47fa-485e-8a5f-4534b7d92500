import React, { useEffect, useRef } from 'react';

const Works = () => {
  const sectionRef = useRef(null);
  const cardsRef = useRef([]);

  useEffect(() => {
    const handleScroll = () => {
      if (!sectionRef.current) return;

      const section = sectionRef.current;
      const sectionTop = section.offsetTop;
      const sectionHeight = section.offsetHeight;
      const scrollY = window.scrollY;
      const windowHeight = window.innerHeight;

      // Calculate progress through the section
      const sectionProgress = Math.max(0, Math.min(1,
        (scrollY - sectionTop + windowHeight) / (sectionHeight + windowHeight)
      ));

      // Update each card based on scroll progress
      cardsRef.current.forEach((card, index) => {
        if (!card) return;

        const cardProgress = Math.max(0, Math.min(1,
          (sectionProgress * workItems.length) - index
        ));

        // Stack effect: cards slide up as you scroll
        const translateY = (1 - cardProgress) * 100;
        const scale = 0.9 + (cardProgress * 0.1);

        card.style.transform = `translateY(${translateY}vh) scale(${scale})`;
        card.style.opacity = cardProgress > 0 ? 1 : 0;
      });
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll(); // Initial call

    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const workItems = [
    {
      id: 1,
      title: "UI/UX Developer and Branding",
      company: "SOULFUL AI",
      description: "An Emerging Start-up by students.",
      type: "fill" // black fill
    },
    {
      id: 2,
      title: "Banner and Logo Designs",
      company: "Various Colleges",
      description: "Logo Creation for start ups and clubs",
      type: "stroke" // black stroke
    },
    {
      id: 3,
      title: "Posters and Designs",
      company: "Ariyatra Tours and Travels",
      description: "Coimbatore",
      type: "fill" // black fill
    },
    {
      id: 4,
      title: "Media Manager",
      company: "Entrepreneurship Development Cell",
      description: "Sri Eshwar College of Engineering",
      type: "stroke" // black stroke
    },
    {
      id: 5,
      title: "Content Manager",
      company: "Program Professor",
      description: "A Youtube Channel to learn JAVA programming",
      type: "fill" // black fill
    }
  ];

  return (
    <div className="works-section" ref={sectionRef}>
      <div className="works-scroll-container">
        {workItems.map((item, index) => (
          <div
            key={item.id}
            ref={el => cardsRef.current[index] = el}
            className={`work-card ${item.type === 'fill' ? 'work-card-fill' : 'work-card-stroke'}`}
            style={{ zIndex: workItems.length - index }}
          >
            <div className="work-content">
              <h3 className="work-title">{item.title}</h3>
              <h4 className="work-company">{item.company}</h4>
              <p className="work-description">{item.description}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Works;


