import React from 'react';
import ScrollStack, { ScrollStackItem } from './ScrollStack';

const Works = () => {
  const workItems = [
    {
      id: 1,
      title: "UI/UX Developer and Branding",
      company: "SOULFUL AI",
      description: "An Emerging Start-up by students.",
      type: "fill" // black fill
    },
    {
      id: 2,
      title: "Banner and Logo Designs",
      company: "Various Colleges",
      description: "Logo Creation for start ups and clubs",
      type: "stroke" // black stroke
    },
    {
      id: 3,
      title: "Posters and Designs",
      company: "Ariyatra Tours and Travels",
      description: "Coimbatore",
      type: "fill" // black fill
    },
    {
      id: 4,
      title: "Media Manager",
      company: "Entrepreneurship Development Cell",
      description: "Sri Eshwar College of Engineering",
      type: "stroke" // black stroke
    },
    {
      id: 5,
      title: "Content Manager",
      company: "Program Professor",
      description: "A Youtube Channel to learn JAVA programming",
      type: "fill" // black fill
    }
  ];

  return (
    <div className="works-section">
      <ScrollStack
        className="works-scroll-stack"
        itemDistance={50}
        itemScale={0.05}
        itemStackDistance={40}
        stackPosition="20%"
        scaleEndPosition="10%"
        baseScale={0.9}
      >
        {workItems.map((item) => (
          <ScrollStackItem
            key={item.id}
            itemClassName={`work-stack-card ${item.type === 'fill' ? 'work-card-fill' : 'work-card-stroke'}`}
          >
            <div className="work-content">
              <h3 className="work-title">{item.title}</h3>
              <h4 className="work-company">{item.company}</h4>
              <p className="work-description">{item.description}</p>
            </div>
          </ScrollStackItem>
        ))}
      </ScrollStack>
    </div>
  );
};

export default Works;


