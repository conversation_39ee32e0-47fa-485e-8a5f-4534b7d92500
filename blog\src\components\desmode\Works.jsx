import React, { useLayoutEffect, useRef, useCallback } from "react";

// Import images
import c1 from '../../assets/desmode/c1.png';
import c2 from '../../assets/desmode/c2.png';
import c3 from '../../assets/desmode/c3.png';
import c4 from '../../assets/desmode/c4.png';
import c5 from '../../assets/desmode/c5.png';

const ScrollStackItem = ({ children, itemClassName = "" }) => (
  <div
    className={`scroll-stack-card relative w-full h-60 my-8 p-8 box-border origin-top will-change-transform ${itemClassName}`.trim()}
    style={{
      backfaceVisibility: 'hidden',
      transformStyle: 'preserve-3d',
      clipPath: 'polygon(0 0, 100% 0, 98% 100%, 2% 100%)',
    }}
  >
    {children}
  </div>
);

const ScrollStack = ({
  children,
  className = "",
  itemDistance = 100,
  itemScale = 0.03,
  itemStackDistance = 30,
  stackPosition = "20%",
  scaleEndPosition = "10%",
  baseScale = 0.85,
  rotationAmount = 0,
  blurAmount = 0,
  onStackComplete,
}) => {
  const scrollerRef = useRef(null);
  const stackCompletedRef = useRef(false);
  const cardsRef = useRef([]);
  const lastTransformsRef = useRef(new Map());
  const isUpdatingRef = useRef(false);

  const calculateProgress = useCallback((scrollTop, start, end) => {
    if (scrollTop < start) return 0;
    if (scrollTop > end) return 1;
    return (scrollTop - start) / (end - start);
  }, []);

  const parsePercentage = useCallback((value, containerHeight) => {
    if (typeof value === 'string' && value.includes('%')) {
      return (parseFloat(value) / 100) * containerHeight;
    }
    return parseFloat(value);
  }, []);

  const updateCardTransforms = useCallback(() => {
    const scroller = scrollerRef.current;
    if (!scroller || !cardsRef.current.length || isUpdatingRef.current) return;

    isUpdatingRef.current = true;

    const scrollTop = scroller.scrollTop;
    const containerHeight = scroller.clientHeight;
    const stackPositionPx = parsePercentage(stackPosition, containerHeight);
    const scaleEndPositionPx = parsePercentage(scaleEndPosition, containerHeight);
    const endElement = scroller.querySelector('.scroll-stack-end');
    const endElementTop = endElement ? endElement.offsetTop : 0;

    cardsRef.current.forEach((card, i) => {
      if (!card) return;

      const cardTop = card.offsetTop;
      const triggerStart = cardTop - stackPositionPx - (itemStackDistance * i);
      const triggerEnd = cardTop - scaleEndPositionPx;
      const pinStart = cardTop - stackPositionPx - (itemStackDistance * i);
      const pinEnd = endElementTop - containerHeight / 2;

      const scaleProgress = calculateProgress(scrollTop, triggerStart, triggerEnd);
      const targetScale = baseScale + (i * itemScale);
      const scale = 1 - scaleProgress * (1 - targetScale);
      const rotation = rotationAmount ? i * rotationAmount * scaleProgress : 0;

      let translateY = 0;
      const isPinned = scrollTop >= pinStart && scrollTop <= pinEnd;

      if (isPinned) {
        translateY = scrollTop - cardTop + stackPositionPx + (itemStackDistance * i);
      } else if (scrollTop > pinEnd) {
        translateY = pinEnd - cardTop + stackPositionPx + (itemStackDistance * i);
      }

      const newTransform = {
        translateY: Math.round(translateY * 100) / 100,
        scale: Math.round(scale * 1000) / 1000,
        rotation: Math.round(rotation * 100) / 100,
      };

      const lastTransform = lastTransformsRef.current.get(i);
      const hasChanged = !lastTransform ||
        Math.abs(lastTransform.translateY - newTransform.translateY) > 0.1 ||
        Math.abs(lastTransform.scale - newTransform.scale) > 0.001 ||
        Math.abs(lastTransform.rotation - newTransform.rotation) > 0.1;

      if (hasChanged) {
        const transform = `translate3d(0, ${newTransform.translateY}px, 0) scale(${newTransform.scale}) rotate(${newTransform.rotation}deg)`;
        card.style.transform = transform;
        lastTransformsRef.current.set(i, newTransform);
      }

      if (i === cardsRef.current.length - 1) {
        const isInView = scrollTop >= pinStart && scrollTop <= pinEnd;
        if (isInView && !stackCompletedRef.current) {
          stackCompletedRef.current = true;
          onStackComplete?.();
        } else if (!isInView && stackCompletedRef.current) {
          stackCompletedRef.current = false;
        }
      }
    });

    isUpdatingRef.current = false;
  }, [
    itemScale,
    itemStackDistance,
    stackPosition,
    scaleEndPosition,
    baseScale,
    rotationAmount,
    blurAmount,
    onStackComplete,
    calculateProgress,
    parsePercentage,
  ]);

  const handleScroll = useCallback(() => {
    updateCardTransforms();
  }, [updateCardTransforms]);

  useLayoutEffect(() => {
    const scroller = scrollerRef.current;
    if (!scroller) return;

    const cards = Array.from(scroller.querySelectorAll(".scroll-stack-card"));
    cardsRef.current = cards;

    cards.forEach((card, i) => {
      if (i < cards.length - 1) {
        card.style.marginBottom = `${itemDistance}px`;
      }
      card.style.willChange = 'transform';
      card.style.transformOrigin = 'top center';
      card.style.backfaceVisibility = 'hidden';
      card.style.transform = 'translateZ(0)';
    });

    scroller.addEventListener('scroll', handleScroll, { passive: true });

    updateCardTransforms();

    return () => {
      scroller.removeEventListener('scroll', handleScroll);
      stackCompletedRef.current = false;
      cardsRef.current = [];
      lastTransformsRef.current.clear();
      isUpdatingRef.current = false;
    };
  }, [itemDistance, handleScroll, updateCardTransforms]);

  return (
    <div
      className={`relative w-full h-full overflow-y-auto overflow-x-visible ${className}`.trim()}
      ref={scrollerRef}
      style={{
        overscrollBehavior: 'contain',
        WebkitOverflowScrolling: 'touch',
        scrollBehavior: 'smooth',
      }}
    >
      <div className="scroll-stack-inner pt-[20vh] px-8 pb-[50rem] min-h-screen">
        {children}
        <div className="scroll-stack-end w-full h-px" />
      </div>
    </div>
  );
};

// Portfolio data with image references
const portfolioItems = [
  {
    title: "UI/UX Developer & Branding",
    company: "SOULFUL AI",
    description: "An Emerging Start-up by students",
    details: "Focused on creating intuitive user experiences and establishing brand identity for an innovative AI startup founded by passionate students.",
    bgColor: "bg-black",
    textColor: "text-white",
    image: c1
  },
  {
    title: "Banner & Logo Designer",
    company: "Various Colleges",
    description: "Event branding and logo creation for startups and clubs",
    details: "Designed impactful visual identities for college events, startup ventures, and student organizations across multiple institutions.",
    bgColor: "bg-white",
    textColor: "text-black",
    image: c2
  },
  {
    title: "Creative Designer",
    company: "Ariyatra Tours & Travels",
    description: "Coimbatore",
    details: "Created engaging posters and marketing materials that capture the essence of travel experiences and destinations.",
    bgColor: "bg-black",
    textColor: "text-white",
    image: c3
  },
  {
    title: "Media Manager",
    company: "Entrepreneurship Development Cell",
    description: "Sri Eshwar College of Engineering",
    details: "Managed digital content strategy and media presence to promote entrepreneurial initiatives and student ventures.",
    bgColor: "bg-white",
    textColor: "text-black",
    image: c4
  },
  {
    title: "Content Manager",
    company: "Program Professor",
    description: "A YouTube Channel for JAVA Programming",
    details: "Managed educational content creation and strategy for a programming-focused YouTube channel, making JAVA accessible to learners.",
    bgColor: "bg-black",
    textColor: "text-white",
    image: c5
  }
];

export default function Works() {
  return (
    <div className="w-full h-screen bg-gray-100">
      <ScrollStack
        itemDistance={120}
        itemScale={0.04}
        itemStackDistance={40}
        stackPosition="25%"
        scaleEndPosition="15%"
        baseScale={0.8}
      >
        {portfolioItems.map((item, index) => (
          <ScrollStackItem
            key={index}
            itemClassName={`${item.bgColor} ${item.textColor} shadow-2xl border-2 border-gray-800`}
          >
            <div className="h-full flex flex-col justify-center">
              <div className="mb-4">
                <h2 className="text-2xl font-bold mb-2 leading-tight">
                  {item.title}
                </h2>
                <h3 className="text-lg font-semibold mb-1 opacity-80">
                  {item.company}
                </h3>
                <p className="text-sm opacity-60 mb-3">
                  {item.description}
                </p>
              </div>
              <p className="text-sm leading-relaxed opacity-90">
                {item.details}
              </p>
              <div className="absolute top-3 right-3">
                <img
                  src={item.image}
                  alt={`Card ${index + 1}`}
                  className="w-6 h-6 object-contain opacity-50"
                  onError={(e) => {
                    e.target.style.display = 'none';
                  }}
                />
              </div>
            </div>
          </ScrollStackItem>
        ))}
      </ScrollStack>
    </div>
  );
}


