import React from 'react';
import ScrollStack, { ScrollStackItem } from './ScrollStack';

const Works = () => {
  const workItems = [
    {
      id: 1,
      title: "UI/UX Developer and Branding",
      company: "SOULFUL AI",
      description: "An Emerging Start-up by students.",
      type: "fill" // black fill
    },
    {
      id: 2,
      title: "Banner and Logo Designs",
      company: "Various Colleges",
      description: "Logo Creation for start ups and clubs",
      type: "stroke" // black stroke
    },
    {
      id: 3,
      title: "Posters and Designs",
      company: "Ariyatra Tours and Travels",
      description: "Coimbatore",
      type: "fill" // black fill
    },
    {
      id: 4,
      title: "Media Manager",
      company: "Entrepreneurship Development Cell",
      description: "Sri Eshwar College of Engineering",
      type: "stroke" // black stroke
    },
    {
      id: 5,
      title: "Content Manager",
      company: "Program Professor",
      description: "A Youtube Channel to learn JAVA programming",
      type: "fill" // black fill
    }
  ];

  return (
    <ScrollStack
      className="w-full h-screen"
      itemDistance={100}
      itemScale={0.03}
      itemStackDistance={30}
      stackPosition="20%"
      scaleEndPosition="10%"
      baseScale={0.85}
    >
      {workItems.map((item) => (
        <ScrollStackItem
          key={item.id}
          itemClassName={`${item.type === 'fill' ? 'bg-black text-white' : 'bg-white text-black border-4 border-black'}`}
        >
          <div className="flex items-center justify-center h-full">
            <div className="text-center max-w-4xl px-8">
              <h3 className="text-4xl md:text-6xl font-bold mb-6 uppercase tracking-wider leading-tight">
                {item.title}
              </h3>
              <h4 className="text-xl md:text-3xl font-medium mb-4 uppercase tracking-wide">
                {item.company}
              </h4>
              <p className="text-base md:text-xl uppercase tracking-wide leading-relaxed">
                {item.description}
              </p>
            </div>
          </div>
        </ScrollStackItem>
      ))}
    </ScrollStack>
  );
};

export default Works;


